
http:
- name: "first-listener"
  address: "0.0.0.0"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "first-route"
    hostname: "*"
    pathMatch:
      exact: "/"
    destination:
      name: "first-route-dest"
      settings:
      - name: backend-hosts-override-header
        protocol: HTTP
        weight: 1
        isHostOverride: true
        hostOverrideConfig:
          overrideHostSources:
          - header: "target-pod"
  - name: "second-route"
    hostname: "*"
    pathMatch:
      exact: "/metadata"
    destination:
      name: "second-route-dest"
      settings:
      - name: backend-hosts-override-metadata
        protocol: HTTP
        weight: 1
        isHostOverride: true
        hostOverrideConfig:
          overrideHostSources:
          - metadata:
              key: "envoy.lb"
              path:
              - key: "target-pod"
  - name: "third-route"
    hostname: "*"
    pathMatch:
      exact: "/multiple"
    destination:
      name: "third-route-dest"
      settings:
      - name: backend-hosts-override-multiple
        protocol: HTTP
        weight: 1
        isHostOverride: true
        hostOverrideConfig:
          overrideHostSources:
          - header: "target-pod"
          - header: "route-strategy"
          - metadata:
              key: "envoy.lb"
              path:
              - key: "override-host"
