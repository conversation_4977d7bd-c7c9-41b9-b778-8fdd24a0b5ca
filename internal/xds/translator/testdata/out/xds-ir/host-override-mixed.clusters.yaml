- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig:
    localityWeightedLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: mixed-route-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          overrideHostSources:
          - header: target-pod
  name: mixed-route-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
